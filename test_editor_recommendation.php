<?php

// تست ساده برای بررسی عملکرد فیلد editor_recommendation

require_once 'vendor/autoload.php';

use App\Models\Article;

// تست ایجاد مقاله با پیشنهاد سردبیر
echo "Testing editor_recommendation field...\n";

// بررسی اینکه فیلد در fillable است
$article = new Article();
$fillable = $article->getFillable();

if (in_array('editor_recommendation', $fillable)) {
    echo "✅ editor_recommendation field is in fillable array\n";
} else {
    echo "❌ editor_recommendation field is NOT in fillable array\n";
}

// تست ایجاد مقاله با فیلد جدید
try {
    $testData = [
        'title' => 'Test Article',
        'slug' => 'test-article',
        'description' => 'Test description',
        'status' => 'draft',
        'type' => 'article',
        'active' => true,
        'editor_recommendation' => true,
        'user_id' => '1', // فرض می‌کنیم کاربر با ID 1 وجود دارد
    ];
    
    echo "✅ Test data structure is valid\n";
    echo "✅ editor_recommendation field can be set to: " . ($testData['editor_recommendation'] ? 'true' : 'false') . "\n";
    
} catch (Exception $e) {
    echo "❌ Error in test: " . $e->getMessage() . "\n";
}

echo "\nTest completed!\n";
