# قابلیت پیشنهاد سردبیر (Editor Recommendation)

این مستند نحوه پیاده‌سازی و استفاده از قابلیت "پیشنهاد سردبیر" در سیستم مدیریت مقالات را توضیح می‌دهد.

## ویژگی‌های پیاده‌سازی شده

### 1. فیلد دیتابیس
- فیلد `editor_recommendation` (boolean) به مدل Article اضافه شده است
- این فیلد در هر دو مدل `App\Models\Article` و `App\Models\Content\Article` تعریف شده است

### 2. چک باکس تعاملی
- چک باکس toggle در لیست مقالات با استفاده از Livewire و Alpine.js
- نمایش وضعیت فعلی مقاله (تیک خورده یا نخورده)
- قابلیت تغییر وضعیت با کلیک روی چک باکس
- نمایش loading spinner هنگام ذخیره تغییرات

### 3. فیلتر مقالات
- دکمه "پیشنهادات سردبیر" در نوار فیلتر
- امکان مشاهده تنها مقالاتی که به عنوان پیشنهاد سردبیر انتخاب شده‌اند

## نحوه استفاده

### برای کاربران
1. در صفحه لیست مقالات، ستون "پیشنهاد سردبیر" را مشاهده کنید
2. برای تغییر وضعیت یک مقاله، روی چک باکس کلیک کنید
3. برای مشاهده تنها پیشنهادات سردبیر، روی دکمه "پیشنهادات سردبیر" کلیک کنید

### برای توسعه‌دهندگان

#### استفاده در کوئری‌ها
```php
// دریافت تنها مقالات پیشنهادی سردبیر
$recommendedArticles = Article::where('editor_recommendation', true)->get();

// دریافت مقالات غیر پیشنهادی
$nonRecommendedArticles = Article::where('editor_recommendation', false)
    ->orWhereNull('editor_recommendation')
    ->get();
```

#### تنظیم وضعیت پیشنهاد سردبیر
```php
// تنظیم مقاله به عنوان پیشنهاد سردبیر
$article = Article::find($id);
$article->editor_recommendation = true;
$article->save();

// حذف از پیشنهادات سردبیر
$article->editor_recommendation = false;
$article->save();
```

## فایل‌های تغییر یافته

### مدل‌ها
- `app/Models/Article.php` - اضافه شدن فیلد به fillable
- `app/Models/Content/Article.php` - اضافه شدن فیلد به fillable

### کامپوننت‌های Livewire
- `app/Livewire/Dashboard/Articles/ArticleIndex.php` - متد toggle و فیلتر
- `app/Livewire/Dashboard/Articles/ArticleCreate.php` - پشتیبانی از فیلد جدید
- `app/Livewire/Dashboard/Articles/ArticleShow.php` - پشتیبانی از فیلد جدید

### View ها
- `resources/views/livewire/dashboard/articles/article-index.blade.php` - چک باکس و فیلتر

## ویژگی‌های فنی

### Alpine.js Integration
- استفاده از Alpine.js برای مدیریت state محلی
- نمایش loading state هنگام ارسال درخواست
- تجربه کاربری روان بدون refresh صفحه

### Livewire Integration
- متد `toggleEditorRecommendation()` برای تغییر وضعیت
- بروزرسانی خودکار لیست پس از تغییر
- مدیریت خطاها و validation

### Performance
- استفاده از eager loading برای بهینه‌سازی کوئری‌ها
- Pagination برای مدیریت حجم بالای داده‌ها

## نکات امنیتی
- تنها کاربران مجاز می‌توانند وضعیت پیشنهاد سردبیر را تغییر دهند
- Validation مناسب برای جلوگیری از تغییرات غیرمجاز
- استفاده از CSRF protection در Livewire

## تست و بررسی
برای تست عملکرد:
1. یک مقاله ایجاد کنید
2. در لیست مقالات، چک باکس پیشنهاد سردبیر را فعال کنید
3. روی دکمه "پیشنهادات سردبیر" کلیک کنید و بررسی کنید که مقاله نمایش داده می‌شود
4. چک باکس را غیرفعال کنید و مجدداً بررسی کنید

## مشکلات احتمالی و راه‌حل‌ها

### مشکل: چک باکس کار نمی‌کند
- بررسی کنید که Alpine.js و Livewire به درستی لود شده‌اند
- Console browser را برای خطاهای JavaScript بررسی کنید

### مشکل: تغییرات ذخیره نمی‌شوند
- مطمئن شوید که فیلد `editor_recommendation` در fillable مدل قرار دارد
- دسترسی‌های کاربر را بررسی کنید

### مشکل: فیلتر کار نمی‌کند
- بررسی کنید که متد `changeStatus` به درستی پیاده‌سازی شده است
- کوئری دیتابیس را برای شرط فیلتر بررسی کنید
