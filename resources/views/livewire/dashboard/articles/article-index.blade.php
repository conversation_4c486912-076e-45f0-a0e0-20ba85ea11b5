<div class="relative min-h-96 rounded-xl">

    <div
        class="overflow-hidden rounded-t-xl"
        x-data="{ fillter: false }"
        wire:ignore
    >

        <div class="rounded-t-xl bg-gray-800 px-3 py-1.5">
            <div class="flex items-center justify-between">
                <div>
                    <span class="block text-base text-white max-md:text-sm">لیست مقالات</span>
                </div>
                <div
                    class="flex items-center gap-4"
                    x-data="playerState()"
                    x-init="checkState()"
                >

                    <div x-show="isPlaying">
                        <span
                            class="text-sm text-gray-500"
                            x-text="countdown"
                        ></span>
                        <span class="text-sm text-gray-500">ثانیه</span>
                    </div>
                    <div
                        class="text-sm font-bold text-gray-100"
                        x-show="isPlaying"
                    >
                        زمان بروزرسانی </div>
                    <button
                        class="flex items-center gap-2 text-white"
                        @click="togglePlay"
                    >
                        <span :class="!isPlaying ? 'text-green-500' : 'text-gray-500'">
                            <svg
                                class="size-6"
                                xmlns="http://www.w3.org/2000/svg"
                                fill="none"
                                viewBox="0 0 24 24"
                                stroke-width="1.5"
                                stroke="currentColor"
                            >
                                <path
                                    stroke-linecap="round"
                                    stroke-linejoin="round"
                                    d="M15.75 5.25v13.5m-7.5-13.5v13.5"
                                />
                            </svg>
                        </span>
                        <span :class="isPlaying ? 'text-green-500' : 'text-gray-500'">
                            <svg
                                class="size-6"
                                xmlns="http://www.w3.org/2000/svg"
                                fill="none"
                                viewBox="0 0 24 24"
                                stroke-width="1.5"
                                stroke="currentColor"
                            >
                                <path
                                    stroke-linecap="round"
                                    stroke-linejoin="round"
                                    d="M5.25 5.653c0-.856.917-1.398 1.667-.986l11.54 6.347a1.125 1.125 0 0 1 0 1.972l-11.54 6.347a1.125 1.125 0 0 1-1.667-.986V5.653Z"
                                />
                            </svg>
                        </span>
                    </button>

                    <button
                        class="flex items-center gap-2 p-2"
                        @click="fillter = !fillter"
                    >
                        <svg
                            class="h-6 w-6 text-white"
                            xmlns="http://www.w3.org/2000/svg"
                            fill="none"
                            viewBox="0 0 24 24"
                            stroke-width="1.5"
                            stroke="currentColor"
                        >
                            <path
                                stroke-linecap="round"
                                stroke-linejoin="round"
                                d="M10.5 6h9.75M10.5 6a1.5 1.5 0 1 1-3 0m3 0a1.5 1.5 0 1 0-3 0M3.75 6H7.5m3 12h9.75m-9.75 0a1.5 1.5 0 0 1-3 0m3 0a1.5 1.5 0 0 0-3 0m-3.75 0H7.5m9-6h3.75m-3.75 0a1.5 1.5 0 0 1-3 0m3 0a1.5 1.5 0 0 0-3 0m-9.75 0h9.75"
                            />
                        </svg>
                        <span class="text-base text-white">فیلتر</span>
                    </button>

                </div>
            </div>
        </div>
        <form
            class="tranfsition-all transform overflow-hidden bg-gray-800 px-6"
            wire:submit="fillter"
            x-cloak
            :class="fillter ? 'h-auto pt-4' : 'h-0'"
        >

            <div class="my-3 grid grid-cols-1 gap-3">
                <div>
                    <label
                        class="mb-2 block text-sm text-white"
                        for="title"
                    >عنوان مقاله:</label>
                    <input
                        class="block w-full rounded-lg border-2 border-gray-800 bg-gray-700 p-2 text-sm text-gray-200 outline-none focus:border-red-600 focus:ring-red-600 dark:border-gray-600 dark:bg-gray-700 dark:text-white dark:placeholder-gray-400 dark:focus:border-red-600 dark:focus:ring-red-600"
                        id="title"
                        type="text"
                        wire:model="data.title"
                    >
                    @error('data.title')
                        <div class="pb-2 pt-3"><span class="mt-2 rounded-lg bg-red-50 p-1 px-2"><span
                                    class="text-sm font-bold text-red-600"
                                >{{ $message }}</span></span></div>
                    @enderror
                </div>

            </div>

            <div class="flex flex-row-reverse items-center gap-3 py-3">

                <button
                    class="rounded-lg bg-red-500 px-6 py-1 text-white transition-all hover:bg-red-600 disabled:bg-gray-100 disabled:text-gray-400"
                    type="submit"
                >
                    <svg
                        class="inline h-4 w-4 animate-spin text-red-600 dark:text-red-500"
                        role="status"
                        aria-hidden="true"
                        wire:loading
                        wire:target="fillter"
                        viewBox="0 0 100 101"
                        fill="none"
                        xmlns="http://www.w3.org/2000/svg"
                    >
                        <path
                            d="M100 50.5908C100 78.2051 77.6142 100.591 50 100.591C22.3858 100.591 0 78.2051 0 50.5908C0 22.9766 22.3858 0.59082 50 0.59082C77.6142 0.59082 100 22.9766 100 50.5908ZM9.08144 50.5908C9.08144 73.1895 27.4013 91.5094 50 91.5094C72.5987 91.5094 90.9186 73.1895 90.9186 50.5908C90.9186 27.9921 72.5987 9.67226 50 9.67226C27.4013 9.67226 9.08144 27.9921 9.08144 50.5908Z"
                            fill="#E5E7EB"
                        />
                        <path
                            d="M93.9676 39.0409C96.393 38.4038 97.8624 35.9116 97.0079 33.5539C95.2932 28.8227 92.871 24.3692 89.8167 20.348C85.8452 15.1192 80.8826 10.7238 75.2124 7.41289C69.5422 4.10194 63.2754 1.94025 56.7698 1.05124C51.7666 0.367541 46.6976 0.446843 41.7345 1.27873C39.2613 1.69328 37.813 4.19778 38.4501 6.62326C39.0873 9.04874 41.5694 10.4717 44.0505 10.1071C47.8511 9.54855 51.7191 9.52689 55.5402 10.0491C60.8642 10.7766 65.9928 12.5457 70.6331 15.2552C75.2735 17.9648 79.3347 21.5619 82.5849 25.841C84.9175 28.9121 86.7997 32.2913 88.1811 35.8758C89.083 38.2158 91.5421 39.6781 93.9676 39.0409Z"
                            fill="currentColor"
                        />
                    </svg>
                    <span class="text-sm">اعمال فیلتر</span>
                </button>
                <button
                    class="rounded-lg bg-gray-300 px-6 py-1 text-gray-600 transition-all hover:bg-gray-200 disabled:bg-gray-100 disabled:text-gray-400"
                    type="button"
                    wire:click="ClearFillter"
                >
                    <svg
                        class="inline h-4 w-4 animate-spin text-red-600 dark:text-red-500"
                        role="status"
                        aria-hidden="true"
                        wire:loading
                        wire:target="ClearFillter"
                        viewBox="0 0 100 101"
                        fill="none"
                        xmlns="http://www.w3.org/2000/svg"
                    >
                        <path
                            d="M100 50.5908C100 78.2051 77.6142 100.591 50 100.591C22.3858 100.591 0 78.2051 0 50.5908C0 22.9766 22.3858 0.59082 50 0.59082C77.6142 0.59082 100 22.9766 100 50.5908ZM9.08144 50.5908C9.08144 73.1895 27.4013 91.5094 50 91.5094C72.5987 91.5094 90.9186 73.1895 90.9186 50.5908C90.9186 27.9921 72.5987 9.67226 50 9.67226C27.4013 9.67226 9.08144 27.9921 9.08144 50.5908Z"
                            fill="#E5E7EB"
                        />
                        <path
                            d="M93.9676 39.0409C96.393 38.4038 97.8624 35.9116 97.0079 33.5539C95.2932 28.8227 92.871 24.3692 89.8167 20.348C85.8452 15.1192 80.8826 10.7238 75.2124 7.41289C69.5422 4.10194 63.2754 1.94025 56.7698 1.05124C51.7666 0.367541 46.6976 0.446843 41.7345 1.27873C39.2613 1.69328 37.813 4.19778 38.4501 6.62326C39.0873 9.04874 41.5694 10.4717 44.0505 10.1071C47.8511 9.54855 51.7191 9.52689 55.5402 10.0491C60.8642 10.7766 65.9928 12.5457 70.6331 15.2552C75.2735 17.9648 79.3347 21.5619 82.5849 25.841C84.9175 28.9121 86.7997 32.2913 88.1811 35.8758C89.083 38.2158 91.5421 39.6781 93.9676 39.0409Z"
                            fill="currentColor"
                        />
                    </svg>
                    <span class="text-sm">حذف فیلترها</span>
                </button>
            </div>
        </form>
    </div>

    <div class="min-h-96 rounded-b-xl bg-white">
        <div class="w-full">
            <div class="flex items-center justify-between gap-3 bg-gray-100 px-3 py-2">
                <div
                    class="flex items-center gap-3 max-md:hidden"
                    x-data="{ status: 'article' }"
                >
                    {{-- <template
                        x-for="status in ['all', 'published', 'draft']"
                        :key="status"
                    >
                        <button
                            @click="$wire.changeStatusList(status)"
                            :class="{
                                'border-green-500 bg-green-100': filter === status,
                                'border-2': true,
                                'rounded-2xl': true,
                                'px-4': true,
                                'py-2': true,
                                'text-sm': true,
                                'flex': true,
                                'shrink-0': true,
                                'items-center': true,
                                'justify-center': true
                            }"
                        >
                            <span
                                class="whitespace-nowrap"
                                x-text="status === 'all' ? 'همه مقالات' : (status === 'published' ? 'منتشر شده' : 'پیش نویس')"
                            ></span>
                        </button>
                    </template> --}}
                    <div class="flex items-center gap-3">
                        <button
                            class="flex shrink-0 items-center justify-center rounded-2xl border-2 px-4 py-2 text-sm"
                            :class="status == 'all' ? 'border-green-500 bg-green-100' : ''"
                            @click="status = 'all'"
                        >
                            <span
                                class="flex items-center gap-3 whitespace-nowrap"
                                wire:click="changeStatus('all')"
                            >
                                <svg
                                    class="inline h-4 w-4 animate-spin text-red-600 dark:text-red-500"
                                    role="status"
                                    aria-hidden="true"
                                    wire:loading
                                    wire:target="changeStatus('all')"
                                    viewBox="0 0 100 101"
                                    fill="none"
                                    xmlns="http://www.w3.org/2000/svg"
                                >
                                    <path
                                        d="M100 50.5908C100 78.2051 77.6142 100.591 50 100.591C22.3858 100.591 0 78.2051 0 50.5908C0 22.9766 22.3858 0.59082 50 0.59082C77.6142 0.59082 100 22.9766 100 50.5908ZM9.08144 50.5908C9.08144 73.1895 27.4013 91.5094 50 91.5094C72.5987 91.5094 90.9186 73.1895 90.9186 50.5908C90.9186 27.9921 72.5987 9.67226 50 9.67226C27.4013 9.67226 9.08144 27.9921 9.08144 50.5908Z"
                                        fill="#E5E7EB"
                                    />
                                    <path
                                        d="M93.9676 39.0409C96.393 38.4038 97.8624 35.9116 97.0079 33.5539C95.2932 28.8227 92.871 24.3692 89.8167 20.348C85.8452 15.1192 80.8826 10.7238 75.2124 7.41289C69.5422 4.10194 63.2754 1.94025 56.7698 1.05124C51.7666 0.367541 46.6976 0.446843 41.7345 1.27873C39.2613 1.69328 37.813 4.19778 38.4501 6.62326C39.0873 9.04874 41.5694 10.4717 44.0505 10.1071C47.8511 9.54855 51.7191 9.52689 55.5402 10.0491C60.8642 10.7766 65.9928 12.5457 70.6331 15.2552C75.2735 17.9648 79.3347 21.5619 82.5849 25.841C84.9175 28.9121 86.7997 32.2913 88.1811 35.8758C89.083 38.2158 91.5421 39.6781 93.9676 39.0409Z"
                                        fill="currentColor"
                                    />
                                </svg>
                                <span>همه دسته بندی ها</span>
                            </span>
                        </button>
                        <button
                            class="flex shrink-0 items-center justify-center rounded-2xl border-2 px-4 py-2 text-sm"
                            :class="status == 'article' ? 'border-green-500 bg-green-100' : ''"
                            @click="status = 'article'"
                        >
                            <span
                                class="flex items-center gap-3 whitespace-nowrap"
                                wire:click="changeStatus('article')"
                            >
                                <svg
                                    class="inline h-4 w-4 animate-spin text-red-600 dark:text-red-500"
                                    role="status"
                                    aria-hidden="true"
                                    wire:loading
                                    wire:target="changeStatus('article')"
                                    viewBox="0 0 100 101"
                                    fill="none"
                                    xmlns="http://www.w3.org/2000/svg"
                                >
                                    <path
                                        d="M100 50.5908C100 78.2051 77.6142 100.591 50 100.591C22.3858 100.591 0 78.2051 0 50.5908C0 22.9766 22.3858 0.59082 50 0.59082C77.6142 0.59082 100 22.9766 100 50.5908ZM9.08144 50.5908C9.08144 73.1895 27.4013 91.5094 50 91.5094C72.5987 91.5094 90.9186 73.1895 90.9186 50.5908C90.9186 27.9921 72.5987 9.67226 50 9.67226C27.4013 9.67226 9.08144 27.9921 9.08144 50.5908Z"
                                        fill="#E5E7EB"
                                    />
                                    <path
                                        d="M93.9676 39.0409C96.393 38.4038 97.8624 35.9116 97.0079 33.5539C95.2932 28.8227 92.871 24.3692 89.8167 20.348C85.8452 15.1192 80.8826 10.7238 75.2124 7.41289C69.5422 4.10194 63.2754 1.94025 56.7698 1.05124C51.7666 0.367541 46.6976 0.446843 41.7345 1.27873C39.2613 1.69328 37.813 4.19778 38.4501 6.62326C39.0873 9.04874 41.5694 10.4717 44.0505 10.1071C47.8511 9.54855 51.7191 9.52689 55.5402 10.0491C60.8642 10.7766 65.9928 12.5457 70.6331 15.2552C75.2735 17.9648 79.3347 21.5619 82.5849 25.841C84.9175 28.9121 86.7997 32.2913 88.1811 35.8758C89.083 38.2158 91.5421 39.6781 93.9676 39.0409Z"
                                        fill="currentColor"
                                    />
                                </svg>
                                <span>مقالات</span>
                            </span>
                        </button>
                        <button
                            class="flex shrink-0 items-center justify-center rounded-2xl border-2 px-4 py-2 text-sm"
                            :class="status == 'services' ? 'border-green-500 bg-green-100' : ''"
                            @click="status = 'services'"
                        >
                            <span
                                class="flex items-center gap-3 whitespace-nowrap"
                                wire:click="changeStatus('services')"
                            >
                                <svg
                                    class="inline h-4 w-4 animate-spin text-red-600 dark:text-red-500"
                                    role="status"
                                    aria-hidden="true"
                                    wire:loading
                                    wire:target="changeStatus('services')"
                                    viewBox="0 0 100 101"
                                    fill="none"
                                    xmlns="http://www.w3.org/2000/svg"
                                >
                                    <path
                                        d="M100 50.5908C100 78.2051 77.6142 100.591 50 100.591C22.3858 100.591 0 78.2051 0 50.5908C0 22.9766 22.3858 0.59082 50 0.59082C77.6142 0.59082 100 22.9766 100 50.5908ZM9.08144 50.5908C9.08144 73.1895 27.4013 91.5094 50 91.5094C72.5987 91.5094 90.9186 73.1895 90.9186 50.5908C90.9186 27.9921 72.5987 9.67226 50 9.67226C27.4013 9.67226 9.08144 27.9921 9.08144 50.5908Z"
                                        fill="#E5E7EB"
                                    />
                                    <path
                                        d="M93.9676 39.0409C96.393 38.4038 97.8624 35.9116 97.0079 33.5539C95.2932 28.8227 92.871 24.3692 89.8167 20.348C85.8452 15.1192 80.8826 10.7238 75.2124 7.41289C69.5422 4.10194 63.2754 1.94025 56.7698 1.05124C51.7666 0.367541 46.6976 0.446843 41.7345 1.27873C39.2613 1.69328 37.813 4.19778 38.4501 6.62326C39.0873 9.04874 41.5694 10.4717 44.0505 10.1071C47.8511 9.54855 51.7191 9.52689 55.5402 10.0491C60.8642 10.7766 65.9928 12.5457 70.6331 15.2552C75.2735 17.9648 79.3347 21.5619 82.5849 25.841C84.9175 28.9121 86.7997 32.2913 88.1811 35.8758C89.083 38.2158 91.5421 39.6781 93.9676 39.0409Z"
                                        fill="currentColor"
                                    />
                                </svg>
                                <span>سرویس ها</span>
                            </span>
                        </button>

                        <button
                            class="flex shrink-0 items-center justify-center rounded-2xl border-2 px-4 py-2 text-sm"
                            :class="status == 'products' ? 'border-green-500 bg-green-100' : ''"
                            @click="status = 'products'"
                        >
                            <span
                                class="flex items-center gap-3 whitespace-nowrap"
                                wire:click="changeStatus('products')"
                            >
                                <svg
                                    class="inline h-4 w-4 animate-spin text-red-600 dark:text-red-500"
                                    role="status"
                                    aria-hidden="true"
                                    wire:loading
                                    wire:target="changeStatus('products')"
                                    viewBox="0 0 100 101"
                                    fill="none"
                                    xmlns="http://www.w3.org/2000/svg"
                                >
                                    <path
                                        d="M100 50.5908C100 78.2051 77.6142 100.591 50 100.591C22.3858 100.591 0 78.2051 0 50.5908C0 22.9766 22.3858 0.59082 50 0.59082C77.6142 0.59082 100 22.9766 100 50.5908ZM9.08144 50.5908C9.08144 73.1895 27.4013 91.5094 50 91.5094C72.5987 91.5094 90.9186 73.1895 90.9186 50.5908C90.9186 27.9921 72.5987 9.67226 50 9.67226C27.4013 9.67226 9.08144 27.9921 9.08144 50.5908Z"
                                        fill="#E5E7EB"
                                    />
                                    <path
                                        d="M93.9676 39.0409C96.393 38.4038 97.8624 35.9116 97.0079 33.5539C95.2932 28.8227 92.871 24.3692 89.8167 20.348C85.8452 15.1192 80.8826 10.7238 75.2124 7.41289C69.5422 4.10194 63.2754 1.94025 56.7698 1.05124C51.7666 0.367541 46.6976 0.446843 41.7345 1.27873C39.2613 1.69328 37.813 4.19778 38.4501 6.62326C39.0873 9.04874 41.5694 10.4717 44.0505 10.1071C47.8511 9.54855 51.7191 9.52689 55.5402 10.0491C60.8642 10.7766 65.9928 12.5457 70.6331 15.2552C75.2735 17.9648 79.3347 21.5619 82.5849 25.841C84.9175 28.9121 86.7997 32.2913 88.1811 35.8758C89.083 38.2158 91.5421 39.6781 93.9676 39.0409Z"
                                        fill="currentColor"
                                    />
                                </svg>
                                <span>محصولات</span>
                            </span>
                        </button>
                        <button
                            class="flex shrink-0 items-center justify-center rounded-2xl border-2 px-4 py-2 text-sm"
                            :class="status == 'editor_recommendations' ? 'border-green-500 bg-green-100' : ''"
                            @click="status = 'editor_recommendations'"
                        >
                            <span
                                class="flex items-center gap-3 whitespace-nowrap"
                                wire:click="changeStatus('editor_recommendations')"
                            >
                                <svg
                                    class="inline h-4 w-4 animate-spin text-red-600 dark:text-red-500"
                                    role="status"
                                    aria-hidden="true"
                                    wire:loading
                                    wire:target="changeStatus('editor_recommendations')"
                                    viewBox="0 0 100 101"
                                    fill="none"
                                    xmlns="http://www.w3.org/2000/svg"
                                >
                                    <path
                                        d="M100 50.5908C100 78.2051 77.6142 100.591 50 100.591C22.3858 100.591 0 78.2051 0 50.5908C0 22.9766 22.3858 0.59082 50 0.59082C77.6142 0.59082 100 22.9766 100 50.5908ZM9.08144 50.5908C9.08144 73.1895 27.4013 91.5094 50 91.5094C72.5987 91.5094 90.9186 73.1895 90.9186 50.5908C90.9186 27.9921 72.5987 9.67226 50 9.67226C27.4013 9.67226 9.08144 27.9921 9.08144 50.5908Z"
                                        fill="#E5E7EB"
                                    />
                                    <path
                                        d="M93.9676 39.0409C96.393 38.4038 97.8624 35.9116 97.0079 33.5539C95.2932 28.8227 92.871 24.3692 89.8167 20.348C85.8452 15.1192 80.8826 10.7238 75.2124 7.41289C69.5422 4.10194 63.2754 1.94025 56.7698 1.05124C51.7666 0.367541 46.6976 0.446843 41.7345 1.27873C39.2613 1.69328 37.813 4.19778 38.4501 6.62326C39.0873 9.04874 41.5694 10.4717 44.0505 10.1071C47.8511 9.54855 51.7191 9.52689 55.5402 10.0491C60.8642 10.7766 65.9928 12.5457 70.6331 15.2552C75.2735 17.9648 79.3347 21.5619 82.5849 25.841C84.9175 28.9121 86.7997 32.2913 88.1811 35.8758C89.083 38.2158 91.5421 39.6781 93.9676 39.0409Z"
                                        fill="currentColor"
                                    />
                                </svg>
                                <span>پیشنهادات سردبیر</span>
                            </span>
                        </button>
                        <button
                            class="flex shrink-0 items-center justify-center rounded-2xl border-2 px-4 py-2 text-sm"
                            :class="status == 'trash' ? 'border-green-500 bg-green-100' : ''"
                            @click="status = 'trash'"
                            wire:click="changeStatus('trash')"
                        >
                            <span
                                class="flex items-center gap-3 whitespace-nowrap"
                                wire:click="changeStatus('trash')"
                            >
                                <svg
                                    class="inline h-5 w-5 text-gray-500 dark:text-red-500"
                                    wire:loading.remove
                                    wire:target="changeStatus('trash')"
                                    xmlns="http://www.w3.org/2000/svg"
                                    fill="none"
                                    viewBox="0 0 24 24"
                                    stroke-width="1.5"
                                    stroke="currentColor"
                                >
                                    <path
                                        stroke-linecap="round"
                                        stroke-linejoin="round"
                                        d="m14.74 9-.346 9m-4.788 0L9.26 9m9.968-3.21c.342.052.682.107 1.022.166m-1.022-.165L18.16 19.673a2.25 2.25 0 0 1-2.244 2.077H8.084a2.25 2.25 0 0 1-2.244-2.077L4.772 5.79m14.456 0a48.108 48.108 0 0 0-3.478-.397m-12 .562c.34-.059.68-.114 1.022-.165m0 0a48.11 48.11 0 0 1 3.478-.397m7.5 0v-.916c0-1.18-.91-2.164-2.09-2.201a51.964 51.964 0 0 0-3.32 0c-1.18.037-2.09 1.022-2.09 2.201v.916m7.5 0a48.667 48.667 0 0 0-7.5 0"
                                    />
                                </svg>

                                <svg
                                    class="inline h-4 w-4 animate-spin text-red-600 dark:text-red-500"
                                    role="status"
                                    aria-hidden="true"
                                    wire:loading
                                    wire:target="changeStatus('trash')"
                                    viewBox="0 0 100 101"
                                    fill="none"
                                    xmlns="http://www.w3.org/2000/svg"
                                >
                                    <path
                                        d="M100 50.5908C100 78.2051 77.6142 100.591 50 100.591C22.3858 100.591 0 78.2051 0 50.5908C0 22.9766 22.3858 0.59082 50 0.59082C77.6142 0.59082 100 22.9766 100 50.5908ZM9.08144 50.5908C9.08144 73.1895 27.4013 91.5094 50 91.5094C72.5987 91.5094 90.9186 73.1895 90.9186 50.5908C90.9186 27.9921 72.5987 9.67226 50 9.67226C27.4013 9.67226 9.08144 27.9921 9.08144 50.5908Z"
                                        fill="#E5E7EB"
                                    />
                                    <path
                                        d="M93.9676 39.0409C96.393 38.4038 97.8624 35.9116 97.0079 33.5539C95.2932 28.8227 92.871 24.3692 89.8167 20.348C85.8452 15.1192 80.8826 10.7238 75.2124 7.41289C69.5422 4.10194 63.2754 1.94025 56.7698 1.05124C51.7666 0.367541 46.6976 0.446843 41.7345 1.27873C39.2613 1.69328 37.813 4.19778 38.4501 6.62326C39.0873 9.04874 41.5694 10.4717 44.0505 10.1071C47.8511 9.54855 51.7191 9.52689 55.5402 10.0491C60.8642 10.7766 65.9928 12.5457 70.6331 15.2552C75.2735 17.9648 79.3347 21.5619 82.5849 25.841C84.9175 28.9121 86.7997 32.2913 88.1811 35.8758C89.083 38.2158 91.5421 39.6781 93.9676 39.0409Z"
                                        fill="currentColor"
                                    />
                                </svg>
                                <span>سطل زباله</span>
                            </span>
                        </button>
                    </div>
                </div>
                @can('show-create-article')
                    <div class="flex items-center gap-3">
                        {{-- <button
                            class="mt-4 inline-flex items-start justify-start rounded-xl bg-gray-200 px-6 py-3 text-gray-700 transition-all hover:bg-gray-100 focus:outline-none focus:ring-2 focus:ring-red-600 focus:ring-offset-2 sm:mt-0"
                            type="button"
                            wire:click="$dispatch('openModal', { component: 'dashboard.articles.categories.categories-index' })"
                        >
                            <span class="flex items-center gap-2">
                                <svg
                                    class="size-4"
                                    xmlns="http://www.w3.org/2000/svg"
                                    fill="none"
                                    viewBox="0 0 24 24"
                                    stroke-width="1.5"
                                    stroke="currentColor"
                                >
                                    <path
                                        stroke-linecap="round"
                                        stroke-linejoin="round"
                                        d="M3.75 12h16.5m-16.5 3.75h16.5M3.75 19.5h16.5M5.625 4.5h12.75a1.875 1.875 0 0 1 0 3.75H5.625a1.875 1.875 0 0 1 0-3.75Z"
                                    />
                                </svg>

                                <p class="text-sm font-medium leading-none">دسته بندی</p>
                            </span>
                        </button> --}}
                        <a
                            class="mt-4 inline-flex items-start justify-start rounded-xl bg-gray-200 px-6 py-3 text-gray-700 transition-all hover:bg-gray-100 focus:outline-none focus:ring-2 focus:ring-red-600 focus:ring-offset-2 sm:mt-0"
                            type="button"
                            href="{{ route('categories') }}"
                        >
                            <span class="flex items-center gap-2">
                                <svg
                                    class="size-4"
                                    xmlns="http://www.w3.org/2000/svg"
                                    fill="none"
                                    viewBox="0 0 24 24"
                                    stroke-width="1.5"
                                    stroke="currentColor"
                                >
                                    <path
                                        stroke-linecap="round"
                                        stroke-linejoin="round"
                                        d="M3.75 12h16.5m-16.5 3.75h16.5M3.75 19.5h16.5M5.625 4.5h12.75a1.875 1.875 0 0 1 0 3.75H5.625a1.875 1.875 0 0 1 0-3.75Z"
                                    />
                                </svg>

                                <p class="text-sm font-medium leading-none">دسته بندی</p>
                            </span>
                        </a>
                        <a
                            class="mt-4 inline-flex items-start justify-start rounded-xl bg-red-500 px-6 py-3 text-white transition-all hover:bg-red-600 focus:outline-none focus:ring-2 focus:ring-red-600 focus:ring-offset-2 sm:mt-0"
                            href="{{ route('article-create') }}"
                        >
                            <span class="flex items-center gap-2">
                                <svg
                                    class="size-4"
                                    xmlns="http://www.w3.org/2000/svg"
                                    fill="none"
                                    viewBox="0 0 24 24"
                                    stroke-width="1.5"
                                    stroke="currentColor"
                                >
                                    <path
                                        stroke-linecap="round"
                                        stroke-linejoin="round"
                                        d="M12 4.5v15m7.5-7.5h-15"
                                    />
                                </svg>

                                <p class="text-sm font-medium leading-none text-white">ایجاد مقاله جدید</p>
                            </span>
                        </a>

                    </div>
                @endcan

            </div>
            <div class="relative bg-white">
                @include('layouts.tools.loading')

                <div class="overflow-x-auto">
                    <table class="w-full whitespace-nowrap">
                        <thead class="bg-gray-100">
                            <tr
                                class="mb-3 rounded border border-gray-100 focus:outline-none"
                                tabindex="0"
                            >

                                <th class="border border-gray-200 p-3 text-center"><span class="text-sm">عنوان
                                        مقاله</span>
                                </th>
                                <th class="border border-gray-200 p-3 text-center"><span class="text-sm">گروه</span>
                                </th>
                                <th class="border border-gray-200 p-3 text-center"><span class="text-sm">صفحه /
                                        اسلاگ</span>
                                </th>
                                <th class="border border-gray-200 p-3 text-center"><span class="text-sm">تعداد
                                        مشاهده</span></th>
                                <th class="border border-gray-200 p-3 text-center"><span class="text-sm">تاریخ
                                        بروزرسانی</span></th>
                                <th class="border border-gray-200 p-3 text-center"><span class="text-sm">تاریخ
                                        انتشار</span></th>
                                <th class="border border-gray-200 p-3 text-center"><span class="text-sm">پیشنهاد
                                        سردبیر</span>
                                </th>
                                <th class="border border-gray-200 p-3 text-center"><span
                                        class="text-sm">نویسنده</span>
                                </th>
                                <th class="border border-gray-200 p-3 text-center"><span class="text-sm">وضعیت</span>
                                </th>
                                <th class="border border-gray-200 p-3 text-center"><span
                                        class="text-sm">تنظیمات</span>
                                </th>
                            </tr>
                        </thead>
                        <tbody>
                            @foreach ($articles as $key => $item)
                                <tr
                                    tabindex="{{ $key }}"
                                    @class([
                                        'h-16',
                                        'rounded',
                                        'border',
                                        'transition-all',
                                        'hover:bg-gray-100',
                                        'focus:outline-none',
                                    ])
                                >

                                    <td class="px-4 text-right">
                                        <span
                                            class="text-sm"
                                            style="display: inline-block; max-width: 250px !important; word-wrap: break-word; white-space: normal;"
                                        >{{ $item->title }}</span>
                                    </td>

                                    <td class="px-2 text-center">
                                        <span class="block text-sm">{{ $item?->category?->title ?? 'نامشخص' }}</span>
                                    </td>
                                    @if ($item->type == 'services')
                                        <td
                                            class="px-2 text-left"
                                            dir="ltr"
                                        >

                                            <span class="block text-sm">{{ $item?->page }}</span>

                                        </td>
                                    @else
                                        <td
                                            class="px-2 text-center"
                                            dir="ltr"
                                        >

                                            <span class="block text-sm">{{ $item?->slug }}</span>

                                        </td>
                                    @endif
                                    <td class="px-2 text-center">
                                        <span
                                            class="block text-sm">{{ $item?->view_count ? formatMoney($item?->view_count) : 0 }}</span>
                                    </td>
                                    <td class="px-2 text-center">
                                        <span class="text-sm">{{ shamsiDate($item->updated_at) }}</span>
                                    </td>
                                    <td class="px-2 text-center">
                                        <span class="text-sm">{{ $item->datePublished }}</span>
                                    </td>
                                    <td class="px-4 text-right">
                                        <div
                                            class="flex items-center"
                                            x-data="{
                                                isChecked: {{ $item->editor_recommendation ? 'true' : 'false' }},
                                                isLoading: false,
                                                toggle() {
                                                    this.isLoading = true;
                                                    this.isChecked = !this.isChecked;
                                                    $wire.toggleEditorRecommendation('{{ $item->id }}').then(() => {
                                                        this.isLoading = false;
                                                    });
                                                }
                                            }"
                                        >
                                            <label
                                                class="relative inline-flex cursor-pointer items-center justify-center"
                                            >
                                                <input
                                                    class="peer sr-only"
                                                    type="checkbox"
                                                    x-model="isChecked"
                                                    @click="toggle()"
                                                    :disabled="isLoading"
                                                >
                                                <div
                                                    class="peer relative h-6 w-11 rounded-full bg-gray-200 after:absolute after:left-[2px] after:top-[2px] after:h-5 after:w-5 after:rounded-full after:border after:border-gray-300 after:bg-white after:transition-all after:content-[''] peer-checked:bg-blue-600 peer-checked:after:translate-x-full peer-checked:after:border-white peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 dark:border-gray-600 dark:bg-gray-700 dark:peer-focus:ring-blue-800"
                                                    :class="{ 'opacity-50': isLoading }"
                                                ></div>
                                                <span
                                                    class="ml-3 text-sm font-medium text-gray-900 dark:text-gray-300"
                                                    x-show="isLoading"
                                                >
                                                    <svg
                                                        class="h-4 w-4 animate-spin text-blue-600"
                                                        xmlns="http://www.w3.org/2000/svg"
                                                        fill="none"
                                                        viewBox="0 0 24 24"
                                                    >
                                                        <circle
                                                            class="opacity-25"
                                                            cx="12"
                                                            cy="12"
                                                            r="10"
                                                            stroke="currentColor"
                                                            stroke-width="4"
                                                        ></circle>
                                                        <path
                                                            class="opacity-75"
                                                            fill="currentColor"
                                                            d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
                                                        ></path>
                                                    </svg>
                                                </span>
                                            </label>
                                        </div>
                                    </td>
                                    <td class="px-2 text-center">
                                        @php
                                            $colors = [
                                                'bg-red-100 text-red-700',
                                                'bg-green-100 text-green-700',
                                                'bg-blue-100 text-blue-700',
                                                'bg-yellow-100 text-yellow-700',
                                                'bg-purple-100 text-purple-700',
                                                'bg-pink-100 text-pink-700',
                                                'bg-indigo-100 text-indigo-700',
                                                'bg-teal-100 text-teal-700',
                                                'bg-emerald-100 text-emerald-700',
                                            ];

                                            $fullname = $item?->author?->fullname ?? 'unknown';
                                            $hash = hexdec(substr(md5($fullname), 0, 6)); // استفاده از md5 برای هش بهتر
                                            $colorClass = $colors[$hash % count($colors)];
                                        @endphp

                                        <span
                                            class="{{ $colorClass }} flex items-center gap-1 rounded-2xl px-2 py-1"
                                        >
                                            <svg
                                                class="size-5"
                                                xmlns="http://www.w3.org/2000/svg"
                                                fill="none"
                                                viewBox="0 0 24 24"
                                                stroke-width="1.5"
                                                stroke="currentColor"
                                            >
                                                <path
                                                    stroke-linecap="round"
                                                    stroke-linejoin="round"
                                                    d="M17.982 18.725A7.488 7.488 0 0 0 12 15.75a7.488 7.488 0 0 0-5.982 2.975m11.963 0a9 9 0 1 0-11.963 0m11.963 0A8.966 8.966 0 0 1 12 21a8.966 8.966 0 0 1-5.982-2.275M15 9.75a3 3 0 1 1-6 0 3 3 0 0 1 6 0Z"
                                                />
                                            </svg>

                                            <span class="text-xs">{{ $item?->author?->fullname ?? '-' }}</span>
                                        </span>
                                    </td>
                                    <td class="px-2 text-center">
                                        @if ($item->status == 'published')
                                            <p class="text-sm font-bold text-green-500">منتشر شده</p>
                                        @else
                                            <p class="text-sm font-bold text-red-500">پیش نویس</p>
                                        @endif
                                    </td>
                                    <td class="px-2 text-center">
                                        <div class="flex w-full items-center justify-center gap-1">

                                            @if ($item?->deleted_at)
                                                <button
                                                    class="flex items-center gap-3 rounded-lg px-2 py-1 transition-all hover:bg-gray-200"
                                                    type="button"
                                                    wire:click="$dispatch('openModal', { component: 'dashboard.articles.article-remove', arguments: { articleId: '{{ $item->id }}', softDelete : true }})"
                                                >
                                                    <span class="flex items-center gap-2">

                                                        <svg
                                                            class="size-5 text-gray-400"
                                                            xmlns="http://www.w3.org/2000/svg"
                                                            fill="none"
                                                            viewBox="0 0 24 24"
                                                            stroke-width="1.5"
                                                            stroke="currentColor"
                                                        >
                                                            <path
                                                                stroke-linecap="round"
                                                                stroke-linejoin="round"
                                                                d="m14.74 9-.346 9m-4.788 0L9.26 9m9.968-3.21c.342.052.682.107 1.022.166m-1.022-.165L18.16 19.673a2.25 2.25 0 0 1-2.244 2.077H8.084a2.25 2.25 0 0 1-2.244-2.077L4.772 5.79m14.456 0a48.108 48.108 0 0 0-3.478-.397m-12 .562c.34-.059.68-.114 1.022-.165m0 0a48.11 48.11 0 0 1 3.478-.397m7.5 0v-.916c0-1.18-.91-2.164-2.09-2.201a51.964 51.964 0 0 0-3.32 0c-1.18.037-2.09 1.022-2.09 2.201v.916m7.5 0a48.667 48.667 0 0 0-7.5 0"
                                                            />
                                                        </svg>

                                                    </span>
                                                    <span class="text-sm text-red-500">حذف دائمی</span>
                                                </button>
                                                <button
                                                    class="flex items-center gap-3 rounded-lg px-2 py-1 transition-all hover:bg-gray-200"
                                                    type="button"
                                                    wire:click="$dispatch('openModal', { component: 'dashboard.articles.article-remove', arguments: { articleId: '{{ $item->id }}', softDelete : true }})"
                                                >
                                                    <span class="flex items-center gap-2">

                                                        <svg
                                                            class="size-5 text-gray-400"
                                                            xmlns="http://www.w3.org/2000/svg"
                                                            fill="none"
                                                            viewBox="0 0 24 24"
                                                            stroke-width="1.5"
                                                            stroke="currentColor"
                                                        >
                                                            <path
                                                                stroke-linecap="round"
                                                                stroke-linejoin="round"
                                                                d="M9 15 3 9m0 0 6-6M3 9h12a6 6 0 0 1 0 12h-3"
                                                            />
                                                        </svg>
                                                        <span class="text-sm">بازیابی</span>
                                                    </span>
                                                </button>
                                            @else
                                                <button
                                                    class="flex items-center gap-3 rounded-lg px-2 py-1 transition-all hover:bg-gray-200"
                                                    type="button"
                                                    wire:click="$dispatch('openModal', { component: 'dashboard.articles.article-remove', arguments: { articleId: '{{ $item->id }}' }})"
                                                >
                                                    <span class="flex items-center gap-2">

                                                        <svg
                                                            class="size-5 text-gray-400"
                                                            xmlns="http://www.w3.org/2000/svg"
                                                            fill="none"
                                                            viewBox="0 0 24 24"
                                                            stroke-width="1.5"
                                                            stroke="currentColor"
                                                        >
                                                            <path
                                                                stroke-linecap="round"
                                                                stroke-linejoin="round"
                                                                d="m14.74 9-.346 9m-4.788 0L9.26 9m9.968-3.21c.342.052.682.107 1.022.166m-1.022-.165L18.16 19.673a2.25 2.25 0 0 1-2.244 2.077H8.084a2.25 2.25 0 0 1-2.244-2.077L4.772 5.79m14.456 0a48.108 48.108 0 0 0-3.478-.397m-12 .562c.34-.059.68-.114 1.022-.165m0 0a48.11 48.11 0 0 1 3.478-.397m7.5 0v-.916c0-1.18-.91-2.164-2.09-2.201a51.964 51.964 0 0 0-3.32 0c-1.18.037-2.09 1.022-2.09 2.201v.916m7.5 0a48.667 48.667 0 0 0-7.5 0"
                                                            />
                                                        </svg>

                                                    </span>
                                                </button>
                                                @if ($item->type == 'services')
                                                    @if ($item->page != '/landing-page')
                                                        <a
                                                            class="flex items-center gap-3 rounded-lg px-2 py-1 transition-all hover:bg-gray-200"
                                                            href="https://khodrox.com/{{ $item->page }}"
                                                            :key="$item - > page"
                                                            target="_blank"
                                                        >
                                                            <span class="flex items-center gap-2">
                                                                <svg
                                                                    class="size-5 text-gray-400"
                                                                    xmlns="http://www.w3.org/2000/svg"
                                                                    fill="none"
                                                                    viewBox="0 0 24 24"
                                                                    stroke-width="1.5"
                                                                    stroke="currentColor"
                                                                >
                                                                    <path
                                                                        stroke-linecap="round"
                                                                        stroke-linejoin="round"
                                                                        d="M2.036 12.322a1.012 1.012 0 0 1 0-.639C3.423 7.51 7.36 4.5 12 4.5c4.638 0 8.573 3.007 9.963 7.178.07.207.07.431 0 .639C20.577 16.49 16.64 19.5 12 19.5c-4.638 0-8.573-3.007-9.963-7.178Z"
                                                                    />
                                                                    <path
                                                                        stroke-linecap="round"
                                                                        stroke-linejoin="round"
                                                                        d="M15 12a3 3 0 1 1-6 0 3 3 0 0 1 6 0Z"
                                                                    />
                                                                </svg>
                                                            </span>
                                                        </a>
                                                    @else
                                                        <a
                                                            class="flex items-center gap-3 rounded-lg px-2 py-1 transition-all hover:bg-gray-200"
                                                            href="https://khodrox.com"
                                                            :key="$item - > page"
                                                            target="_blank"
                                                        >
                                                            <span class="flex items-center gap-2">
                                                                <svg
                                                                    class="size-5 text-gray-400"
                                                                    xmlns="http://www.w3.org/2000/svg"
                                                                    fill="none"
                                                                    viewBox="0 0 24 24"
                                                                    stroke-width="1.5"
                                                                    stroke="currentColor"
                                                                >
                                                                    <path
                                                                        stroke-linecap="round"
                                                                        stroke-linejoin="round"
                                                                        d="M2.036 12.322a1.012 1.012 0 0 1 0-.639C3.423 7.51 7.36 4.5 12 4.5c4.638 0 8.573 3.007 9.963 7.178.07.207.07.431 0 .639C20.577 16.49 16.64 19.5 12 19.5c-4.638 0-8.573-3.007-9.963-7.178Z"
                                                                    />
                                                                    <path
                                                                        stroke-linecap="round"
                                                                        stroke-linejoin="round"
                                                                        d="M15 12a3 3 0 1 1-6 0 3 3 0 0 1 6 0Z"
                                                                    />
                                                                </svg>

                                                            </span>
                                                        </a>
                                                    @endif
                                                @else
                                                    <a
                                                        class="flex items-center gap-3 rounded-lg px-2 py-1 transition-all hover:bg-gray-200"
                                                        href="https://khodrox.com/blog/{{ $item->slug }}"
                                                        target="_blank"
                                                    >
                                                        <span class="flex items-center gap-2">
                                                            <svg
                                                                class="size-5 text-gray-400"
                                                                xmlns="http://www.w3.org/2000/svg"
                                                                fill="none"
                                                                viewBox="0 0 24 24"
                                                                stroke-width="1.5"
                                                                stroke="currentColor"
                                                            >
                                                                <path
                                                                    stroke-linecap="round"
                                                                    stroke-linejoin="round"
                                                                    d="M2.036 12.322a1.012 1.012 0 0 1 0-.639C3.423 7.51 7.36 4.5 12 4.5c4.638 0 8.573 3.007 9.963 7.178.07.207.07.431 0 .639C20.577 16.49 16.64 19.5 12 19.5c-4.638 0-8.573-3.007-9.963-7.178Z"
                                                                />
                                                                <path
                                                                    stroke-linecap="round"
                                                                    stroke-linejoin="round"
                                                                    d="M15 12a3 3 0 1 1-6 0 3 3 0 0 1 6 0Z"
                                                                />
                                                            </svg>
                                                        </span>
                                                    </a>
                                                @endif
                                                @can('show-show-article')
                                                    <a
                                                        class="flex items-center gap-3 rounded-lg bg-gray-100 px-4 py-1 transition-all hover:bg-gray-200"
                                                        href="{{ route('article-show', $item->id) }}"
                                                    >
                                                        <span class="flex items-center gap-3">
                                                            <svg
                                                                class="size-4 text-gray-500"
                                                                xmlns="http://www.w3.org/2000/svg"
                                                                fill="none"
                                                                viewBox="0 0 24 24"
                                                                stroke-width="1.5"
                                                                stroke="currentColor"
                                                            >
                                                                <path
                                                                    stroke-linecap="round"
                                                                    stroke-linejoin="round"
                                                                    d="m16.862 4.487 1.687-1.688a1.875 1.875 0 1 1 2.652 2.652L10.582 16.07a4.5 4.5 0 0 1-1.897 1.13L6 18l.8-2.685a4.5 4.5 0 0 1 1.13-1.897l8.932-8.931Zm0 0L19.5 7.125M18 14v4.75A2.25 2.25 0 0 1 15.75 21H5.25A2.25 2.25 0 0 1 3 18.75V8.25A2.25 2.25 0 0 1 5.25 6H10"
                                                                />
                                                            </svg>

                                                            <span class="text-sm text-gray-600">ویرایش</span>
                                                        </span>
                                                    </a>
                                                @endcan
                                            @endif
                                        </div>
                                    </td>

                                </tr>
                            @endforeach
                        </tbody>
                    </table>

                </div>
                <div
                    class="w-full p-4"
                    wire:loading.remove
                >
                    {{ $articles->links(data: ['dark' => false]) }}
                </div>
                @if (isset($articles) && $articles->count() == 0)
                    <div class="p-5">
                        <div
                            class="flex flex-col items-center justify-center gap-8 rounded-xl border-2 border-dashed border-gray-200 p-16 text-center">
                            <svg
                                class="size-24 text-gray-300"
                                xmlns="http://www.w3.org/2000/svg"
                                fill="none"
                                viewBox="0 0 24 24"
                                stroke-width="1.5"
                                stroke="currentColor"
                            >
                                <path
                                    stroke-linecap="round"
                                    stroke-linejoin="round"
                                    d="M8.25 7.5V6.108c0-1.135.845-2.098 1.976-2.192.373-.03.748-.057 1.123-.08M15.75 18H18a2.25 2.25 0 0 0 2.25-2.25V6.108c0-1.135-.845-2.098-1.976-2.192a48.424 48.424 0 0 0-1.123-.08M15.75 18.75v-1.875a3.375 3.375 0 0 0-3.375-3.375h-1.5a1.125 1.125 0 0 1-1.125-1.125v-1.5A3.375 3.375 0 0 0 6.375 7.5H5.25m11.9-3.664A2.251 2.251 0 0 0 15 2.25h-1.5a2.251 2.251 0 0 0-2.15 1.586m5.8 0c.065.21.1.433.1.664v.75h-6V4.5c0-.231.035-.454.1-.664M6.75 7.5H4.875c-.621 0-1.125.504-1.125 1.125v12c0 .621.504 1.125 1.125 1.125h9.75c.621 0 1.125-.504 1.125-1.125V16.5a9 9 0 0 0-9-9Z"
                                />
                            </svg>

                            <p class="text-base text-gray-400">هیچ مقاله ی یافت نشد!</p>
                        </div>
                    </div>
                @endif

            </div>
        </div>

    </div>
</div>
